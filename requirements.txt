aiohttp==3.9.5
aiosignal==1.3.1
annotated-types==0.7.0
anyio==4.4.0
async-timeout==4.0.3
attrs==23.2.0
anthropic==0.39.0
certifi==2024.6.2
beautifulsoup4~=4.12.3
cffi==1.16.0
charset-normalizer==3.3.2
click==8.1.7
cryptography==42.0.8
dataclasses-json==0.6.7
diskcache==5.6.3
distro==1.9.0
dnspython==2.6.1
email_validator==2.2.0
exceptiongroup==1.2.2
fastapi==0.111.0
fastapi-cli==0.0.4
frozenlist==1.4.1
greenlet==3.0.3
h11==0.14.0
httpcore==1.0.5
httptools==0.6.1
httpx==0.27.0
idna==3.7
Jinja2==3.1.4
joblib==1.4.2
jsonpatch==1.33
jsonpointer==3.0.0
langchain==0.3.0
langchain-community==0.3.0
langchain-core==0.3.0
langchain-openai==0.2.0
langchain-pinecone==0.2.0
langchain-text-splitters==0.3.0
langchain-anthropic==0.2.0
langchain-huggingface==0.1.0
langchain-perplexity
lxml
markdown-it-py==3.0.0
MarkupSafe==2.1.5
marshmallow==3.21.3
mdurl==0.1.2
multidict==6.0.5
mypy-extensions==1.0.0
numpy==1.26.4
openai==1.40.0
orjson==3.10.5
packaging==24.1
pdfminer.six==20231228
pdfplumber==0.11.1
pillow==10.3.0
pinecone==4.0.0
pinecone-client==5.0.0
pycparser==2.22
pydantic==2.7.4
pydantic_core==2.18.4
Pygments==2.18.0
PyMuPDF==1.24.9
PyMuPDFb==1.24.9
PyPDF2==3.0.1
pypdfium2==4.30.0
python-dotenv==1.0.1
python-multipart==0.0.9
PyYAML==6.0.1
redis==5.0.8
regex==2024.5.15
requests==2.32.3
rich==13.7.1
scikit-learn==1.5.0
scipy==1.13.1
shellingham==1.5.4
sniffio==1.3.1
SQLAlchemy==2.0.31
pymysql==1.1.0
itsdangerous==2.1.2
starlette==0.37.2
tenacity==8.4.1
threadpoolctl==3.5.0
tiktoken==0.7.0
tqdm==4.66.4
typer==0.12.3
typing-inspect==0.9.0
typing_extensions==4.12.2
ujson==5.10.0
urllib3==2.2.2
uvicorn==0.30.1
uvloop==0.19.0
watchfiles==0.22.0
websockets==12.0
yarl==1.9.4
google-api-core==2.24.0
google-auth==2.37.0
google-cloud-texttospeech==2.23.0
googleapis-common-protos==1.66.0
grpcio==1.69.0
grpcio-status==1.69.0
proto-plus==1.25.0
protobuf==5.29.2
pyasn1==0.6.1
pyasn1_modules==0.4.1
pydub==0.25.1
rsa==4.9
bs4~=0.0.2
opencv-python~=*********
pytesseract~=0.3.13
pdf2image~=1.17.0
aiofiles==23.2.1
moviepy==1.0.3
selenium==4.15.2
webdriver-manager==4.0.1
pytest==7.4.3
pytest-asyncio==0.21.1
bcrypt==4.0.1
matplotlib~=3.8.4
pandas~=2.2.2
psutil==5.9.8
googlesearch-python