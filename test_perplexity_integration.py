#!/usr/bin/env python3
"""
Test script to verify Perplexity LLM integration
"""

import sys
import os

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import llm_config
from llm_manager.llm_factory import LLMFactory
from llm_manager.llm_variables import perplexity_llm, perplexity_model


def test_perplexity_integration():
    """Test the Perplexity LLM integration"""
    
    print("Testing Perplexity LLM Integration...")
    print("=" * 50)
    
    try:
        # Initialize LLM factory
        llm_factory = LLMFactory(llm_config)
        print("✓ LLM Factory initialized successfully")
        
        # Get Perplexity LLM instance
        perplexity_llm_instance = llm_factory.get_llm(perplexity_llm, perplexity_model)
        print("✓ Perplexity LLM instance created successfully")
        
        # Test a simple query
        test_prompt = "What is the capital of France?"
        print(f"\nTesting with prompt: '{test_prompt}'")
        
        response = perplexity_llm_instance.invoke(test_prompt)
        print(f"✓ Response received: {response.content[:100]}...")
        
        print("\n" + "=" * 50)
        print("✅ Perplexity integration test PASSED!")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import Error: {e}")
        print("Please install langchain-perplexity: pip install langchain-perplexity")
        return False
        
    except Exception as e:
        print(f"❌ Error: {e}")
        print("Please check your PPLX_API_KEY in config.py")
        return False


if __name__ == "__main__":
    success = test_perplexity_integration()
    sys.exit(0 if success else 1)
